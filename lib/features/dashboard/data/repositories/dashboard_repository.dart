import '../../../../shared/services/supabase_service.dart';
import '../../domain/models/user_stats.dart';
import '../../domain/models/today_workout.dart';

class DashboardRepository {
  /// Get user statistics from Supabase
  Future<UserStats> getUserStats() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get completed workouts for streak calculation
      final completedWorkouts = await SupabaseService.client
          .from('completed_workouts')
          .select('date_completed, duration, calories_burned')
          .eq('user_id', userId)
          .order('date_completed', ascending: false);

      // Calculate current streak
      final currentStreak = _calculateStreak(completedWorkouts);

      // Get weekly activity (last 7 days)
      final weekStart = DateTime.now().subtract(const Duration(days: 7));
      final weeklyWorkouts = completedWorkouts
          .where((workout) {
            final date = DateTime.parse(workout['date_completed']);
            return date.isAfter(weekStart);
          })
          .toList();

      // Calculate weekly stats
      final weeklyCalories = weeklyWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );

      final totalDuration = weeklyWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );

      final averageDuration = weeklyWorkouts.isNotEmpty 
          ? totalDuration / weeklyWorkouts.length 
          : 0.0;

      // Generate weekly activity data
      final weeklyActivity = _generateWeeklyActivity(weeklyWorkouts);

      // Get last workout date
      final lastWorkoutDate = completedWorkouts.isNotEmpty
          ? DateTime.parse(completedWorkouts.first['date_completed'])
          : DateTime.now().subtract(const Duration(days: 365));

      // Check if user has workout today
      final today = DateTime.now();
      final hasWorkoutToday = completedWorkouts.any((workout) {
        final date = DateTime.parse(workout['date_completed']);
        return date.year == today.year &&
               date.month == today.month &&
               date.day == today.day;
      });

      return UserStats(
        currentStreak: currentStreak,
        weeklyWorkouts: weeklyWorkouts.length,
        weeklyCalories: weeklyCalories,
        totalWorkouts: completedWorkouts.length,
        averageWorkoutDuration: averageDuration,
        weeklyActivity: weeklyActivity,
        lastWorkoutDate: lastWorkoutDate,
        hasWorkoutToday: hasWorkoutToday,
        workoutsThisWeek: weeklyWorkouts.length,
        totalCalories: weeklyCalories,
      );
    } catch (e) {
      // Return mock user stats for development/demo
      return _createMockUserStats();
    }
  }

  /// Get today's assigned workout
  Future<TodayWorkout?> getTodayWorkout() async {
    try {
      final userId = SupabaseService.currentUser?.id;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Get user's active workout (limit to 1 and take first)
      final workoutResponse = await SupabaseService.client
          .from('workouts')
          .select('''
            id,
            name,
            ai_description,
            is_completed,
            start_time,
            end_time,
            workout_exercises (
              id,
              name,
              sets,
              reps,
              weight,
              rest_interval,
              completed,
              exercises (
                id,
                name,
                description,
                video_url,
                vertical_video,
                instructions
              )
            )
          ''')
          .eq('user_id', userId)
          .eq('is_active', true)
          .limit(1);

      final workoutData = workoutResponse.isNotEmpty ? workoutResponse.first : null;

      if (workoutData == null) {
        // Return mock workout data for development/demo
        return _createMockWorkout();
      }

      // Convert to TodayWorkout model
      final exercises = (workoutData['workout_exercises'] as List)
          .map((exerciseData) => _mapToWorkoutExercise(exerciseData))
          .toList();

      final totalSets = exercises.fold<int>(
        0,
        (sum, exercise) => sum + exercise.sets,
      );

      // Estimate duration (2 minutes per set + rest intervals)
      final estimatedDuration = exercises.fold<int>(
        0,
        (sum, exercise) => sum + (exercise.sets * 2) + 
                          ((exercise.sets - 1) * (exercise.restInterval ~/ 60)),
      );

      // Estimate calories (rough calculation based on duration)
      final estimatedCalories = (estimatedDuration * 8).round();

      return TodayWorkout(
        id: workoutData['id'],
        name: workoutData['name'],
        description: workoutData['ai_description'] ?? 'Your personalized workout',
        estimatedDuration: estimatedDuration,
        estimatedCalories: estimatedCalories,
        totalSets: totalSets,
        exercises: exercises,
        isCompleted: workoutData['is_completed'] ?? false,
        isStarted: workoutData['start_time'] != null,
        startedAt: workoutData['start_time'] != null
            ? DateTime.parse(workoutData['start_time'])
            : null,
        completedAt: workoutData['end_time'] != null
            ? DateTime.parse(workoutData['end_time'])
            : null,
      );
    } catch (e) {
      throw Exception('Failed to get today\'s workout: $e');
    }
  }

  /// Calculate current workout streak
  int _calculateStreak(List<dynamic> completedWorkouts) {
    if (completedWorkouts.isEmpty) return 0;

    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    // Check if user worked out today
    final today = completedWorkouts.any((workout) {
      final date = DateTime.parse(workout['date_completed']);
      return date.year == currentDate.year &&
             date.month == currentDate.month &&
             date.day == currentDate.day;
    });

    if (!today) {
      // If no workout today, start checking from yesterday
      currentDate = currentDate.subtract(const Duration(days: 1));
    }

    // Count consecutive days with workouts
    for (int i = 0; i < completedWorkouts.length; i++) {
      final workoutDate = DateTime.parse(completedWorkouts[i]['date_completed']);
      
      if (workoutDate.year == currentDate.year &&
          workoutDate.month == currentDate.month &&
          workoutDate.day == currentDate.day) {
        streak++;
        currentDate = currentDate.subtract(const Duration(days: 1));
      } else if (workoutDate.isBefore(currentDate)) {
        break;
      }
    }

    return streak;
  }

  /// Generate weekly activity data
  List<WeeklyActivity> _generateWeeklyActivity(List<dynamic> weeklyWorkouts) {
    final List<WeeklyActivity> activity = [];
    final now = DateTime.now();

    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayWorkouts = weeklyWorkouts.where((workout) {
        final workoutDate = DateTime.parse(workout['date_completed']);
        return workoutDate.year == date.year &&
               workoutDate.month == date.month &&
               workoutDate.day == date.day;
      }).toList();

      final totalMinutes = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['duration'] as int? ?? 0),
      );

      final totalCalories = dayWorkouts.fold<int>(
        0,
        (sum, workout) => sum + (workout['calories_burned'] as int? ?? 0),
      );

      activity.add(WeeklyActivity(
        day: _getDayAbbreviation(date.weekday),
        minutes: totalMinutes,
        calories: totalCalories,
        date: date,
      ));
    }

    return activity;
  }

  /// Convert workout exercise data to TodayWorkoutExercise model
  TodayWorkoutExercise _mapToWorkoutExercise(Map<String, dynamic> data) {
    final exerciseData = data['exercises'];
    final reps = (data['reps'] as List?)?.cast<int>() ?? [10];
    final weights = (data['weight'] as List?)?.cast<double>() ?? [0.0];

    return TodayWorkoutExercise(
      id: data['id'],
      name: data['name'],
      description: exerciseData?['description'] ?? '',
      sets: data['sets'] ?? 3,
      reps: reps,
      weights: weights,
      videoUrl: exerciseData?['video_url'],
      thumbnailUrl: exerciseData?['vertical_video'],
      instructions: exerciseData?['instructions'],
      restInterval: data['rest_interval'] ?? 60,
      isCompleted: data['completed'] ?? false,
    );
  }

  /// Get day abbreviation for weekday
  String _getDayAbbreviation(int weekday) {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }

  /// Create mock workout data for development/demo
  TodayWorkout _createMockWorkout() {
    final mockExercises = [
      TodayWorkoutExercise(
        id: 'mock_1',
        name: 'Bent Over Row',
        description: 'Pull the barbell towards your lower chest while keeping your back straight.',
        sets: 3,
        reps: [12, 10, 8],
        weights: [45.0, 50.0, 55.0],
        restInterval: 90,
        instructions: 'Keep your core tight and pull with your back muscles, not your arms.',
      ),
      TodayWorkoutExercise(
        id: 'mock_2',
        name: 'Push-ups',
        description: 'Classic bodyweight exercise for chest, shoulders, and triceps.',
        sets: 3,
        reps: [15, 12, 10],
        weights: [0.0, 0.0, 0.0], // Bodyweight
        restInterval: 60,
        instructions: 'Keep your body in a straight line from head to heels.',
      ),
      TodayWorkoutExercise(
        id: 'mock_3',
        name: 'Squats',
        description: 'Fundamental lower body exercise targeting quads, glutes, and hamstrings.',
        sets: 4,
        reps: [15, 12, 10, 8],
        weights: [95.0, 115.0, 135.0, 155.0],
        restInterval: 120,
        instructions: 'Keep your chest up and knees tracking over your toes.',
      ),
      TodayWorkoutExercise(
        id: 'mock_4',
        name: 'Plank',
        description: 'Core strengthening exercise that builds stability.',
        sets: 3,
        reps: [60, 45, 30], // seconds
        weights: [0.0, 0.0, 0.0], // Bodyweight
        restInterval: 60,
        instructions: 'Keep your body straight and engage your core muscles.',
      ),
    ];

    return TodayWorkout(
      id: 'mock_workout_1',
      name: 'Upper Body Power',
      description: 'A balanced upper body workout focusing on strength and muscle building. Perfect for building functional strength.',
      estimatedDuration: 45,
      estimatedCalories: 320,
      totalSets: 13,
      exercises: mockExercises,
      isCompleted: false,
      isStarted: false,
    );
  }

  /// Create mock user stats for development/demo
  UserStats _createMockUserStats() {
    final now = DateTime.now();
    final mockWeeklyActivity = List.generate(7, (index) {
      final date = now.subtract(Duration(days: 6 - index));
      return WeeklyActivity(
        day: _getDayAbbreviation(date.weekday),
        minutes: index % 2 == 0 ? 45 : 0, // Alternate workout days
        calories: index % 2 == 0 ? 320 : 0,
        date: date,
      );
    });

    return UserStats(
      currentStreak: 3,
      weeklyWorkouts: 3,
      weeklyCalories: 960,
      totalWorkouts: 24,
      averageWorkoutDuration: 42.5,
      weeklyActivity: mockWeeklyActivity,
      lastWorkoutDate: now.subtract(const Duration(days: 1)),
      hasWorkoutToday: false,
      workoutsThisWeek: 3,
      totalCalories: 960,
    );
  }
}
