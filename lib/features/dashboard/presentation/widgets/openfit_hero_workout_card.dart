import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../shared/widgets/orange_gradient_button.dart';
import '../../../../core/theme/color_palette.dart';
import '../../../../core/theme/spacing.dart';
import '../../domain/models/today_workout.dart';

/// OpenFit hero workout card with glass morphism design and orange gradient
class OpenFitHeroWorkoutCard extends StatefulWidget {
  final TodayWorkout? workout;
  final VoidCallback? onStartWorkout;
  final VoidCallback? onViewDetails;

  const OpenFitHeroWorkoutCard({
    super.key,
    this.workout,
    this.onStartWorkout,
    this.onViewDetails,
  });

  @override
  State<OpenFitHeroWorkoutCard> createState() => _OpenFitHeroWorkoutCardState();
}

class _OpenFitHeroWorkoutCardState extends State<OpenFitHeroWorkoutCard>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late AnimationController _buttonController;
  late Animation<double> _shimmerAnimation;
  late Animation<double> _buttonScaleAnimation;
  late Animation<Color?> _buttonColorAnimation;

  bool _isButtonPressed = false;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    _buttonScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    ));

    _buttonColorAnimation = ColorTween(
      begin: AppColorPalette.primaryOrange,
      end: AppColorPalette.primaryOrange.withOpacity(0.8),
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.easeInOut,
    ));

    if (widget.workout == null) {
      _shimmerController.repeat();
    }
  }

  @override
  void didUpdateWidget(OpenFitHeroWorkoutCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.workout != null && oldWidget.workout == null) {
      _shimmerController.stop();
    } else if (widget.workout == null && oldWidget.workout != null) {
      _shimmerController.repeat();
    }
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  void _handleButtonPress() async {
    if (_isButtonPressed) return;

    setState(() {
      _isButtonPressed = true;
    });

    // Button press animation
    await _buttonController.forward();

    // Trigger haptic feedback and callback
    HapticFeedback.mediumImpact();
    widget.onStartWorkout?.call();

    // Reset button after a short delay
    await Future.delayed(const Duration(milliseconds: 100));
    if (mounted) {
      await _buttonController.reverse();
      setState(() {
        _isButtonPressed = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.workout == null) {
      return _buildLoadingCard();
    }

    return _buildWorkoutCard();
  }

  Widget _buildWorkoutCard() {
    final workout = widget.workout!;
    final theme = Theme.of(context);

    return Container(
      height: 240,
      child: Stack(
        children: [
          // Background image with gradient overlay
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: Stack(
                children: [
                  // Background image
                  if (workout.backgroundImageUrl != null)
                    Image.network(
                      workout.backgroundImageUrl!,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: double.infinity,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            gradient: AppColorPalette.primaryGradient,
                          ),
                        );
                      },
                    )
                  else
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppColorPalette.primaryOrange,
                            AppColorPalette.primaryOrangeLight,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                    ),
                  
                  // Dark gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.black.withOpacity(0.7),
                          Colors.black.withOpacity(0.3),
                        ],
                        begin: Alignment.bottomCenter,
                        end: Alignment.topCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Glass morphism content
          Positioned.fill(
            child: GlassMorphismCard(
              borderRadius: 20,
              padding: const EdgeInsets.all(AppSpacing.lg),
              gradientColors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Today\'s Workout',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (workout.isCompleted)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpacing.sm,
                            vertical: AppSpacing.xs,
                          ),
                          decoration: BoxDecoration(
                            color: AppColorPalette.successGreen,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'COMPLETED',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: AppSpacing.sm),

                  // Workout name
                  Text(
                    workout.name.toUpperCase(),
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 1.2,
                    ),
                  ),

                  const SizedBox(height: AppSpacing.md),

                  // Quick stats row
                  Row(
                    children: [
                      _buildStatItem(
                        icon: Icons.schedule,
                        value: '${workout.estimatedDuration}',
                        unit: 'min',
                      ),
                      const SizedBox(width: AppSpacing.lg),
                      _buildStatItem(
                        icon: Icons.local_fire_department,
                        value: '${workout.estimatedCalories}',
                        unit: 'cal',
                      ),
                      const SizedBox(width: AppSpacing.lg),
                      _buildStatItem(
                        icon: Icons.fitness_center,
                        value: '${workout.totalSets}',
                        unit: 'sets',
                      ),
                    ],
                  ),

                  const Spacer(),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: SecondaryButton(
                          text: 'DETAILS',
                          onPressed: widget.onViewDetails,
                          height: 48,
                          borderColor: Colors.white.withOpacity(0.3),
                          textColor: Colors.white,
                        ),
                      ),
                      const SizedBox(width: AppSpacing.md),
                      Expanded(
                        flex: 2,
                        child: AnimatedBuilder(
                          animation: _buttonController,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: _buttonScaleAnimation.value,
                              child: Container(
                                height: 48,
                                decoration: BoxDecoration(
                                  gradient: workout.isCompleted
                                      ? LinearGradient(
                                          colors: [
                                            AppColorPalette.successGreen,
                                            AppColorPalette.successGreen.withOpacity(0.8),
                                          ],
                                        )
                                      : LinearGradient(
                                          colors: [
                                            _buttonColorAnimation.value ?? AppColorPalette.primaryOrange,
                                            (_buttonColorAnimation.value ?? AppColorPalette.primaryOrange).withOpacity(0.8),
                                          ],
                                        ),
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: (workout.isCompleted
                                          ? AppColorPalette.successGreen
                                          : AppColorPalette.primaryOrange).withOpacity(0.3),
                                      blurRadius: _isButtonPressed ? 8 : 12,
                                      offset: Offset(0, _isButtonPressed ? 2 : 4),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: workout.isCompleted ? null : _handleButtonPress,
                                    borderRadius: BorderRadius.circular(12),
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            workout.isCompleted
                                                ? Icons.check
                                                : Icons.play_arrow,
                                            color: Colors.white,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Text(
                                            workout.isCompleted ? 'COMPLETED' : 'START WORKOUT',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              letterSpacing: 0.5,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String value,
    required String unit,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          color: AppColorPalette.primaryOrangeLight,
          size: 18,
        ),
        const SizedBox(width: AppSpacing.xs),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: value,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextSpan(
                text: ' $unit',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.8),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingCard() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          height: 240,
          child: GlassMorphismCard(
            borderRadius: 20,
            padding: const EdgeInsets.all(AppSpacing.lg),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildShimmerBox(width: 120, height: 16),
                    const SizedBox(height: AppSpacing.md),
                    _buildShimmerBox(width: 200, height: 32),
                    const SizedBox(height: AppSpacing.lg),
                    Row(
                      children: [
                        _buildShimmerBox(width: 60, height: 16),
                        const SizedBox(width: AppSpacing.lg),
                        _buildShimmerBox(width: 60, height: 16),
                        const SizedBox(width: AppSpacing.lg),
                        _buildShimmerBox(width: 60, height: 16),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Expanded(child: _buildShimmerBox(height: 48)),
                        const SizedBox(width: AppSpacing.md),
                        Expanded(flex: 2, child: _buildShimmerBox(height: 48)),
                      ],
                    ),
                  ],
                ),
                
                // Shimmer overlay
                Positioned.fill(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(20),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.transparent,
                            Colors.white.withOpacity(0.1),
                            Colors.transparent,
                          ],
                          stops: const [0.0, 0.5, 1.0],
                          begin: Alignment(-1.0 + _shimmerAnimation.value, 0.0),
                          end: Alignment(1.0 + _shimmerAnimation.value, 0.0),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildShimmerBox({double? width, required double height}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
}
