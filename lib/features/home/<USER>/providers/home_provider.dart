import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/services/supabase_service.dart';
import '../../data/repositories/home_repository.dart';
import '../../data/models/home_data.dart';

final homeRepositoryProvider = Provider<HomeRepository>((ref) {
  final supabase = ref.watch(supabaseClientProvider);
  return HomeRepository(supabase);
});

final homeDataProvider = FutureProvider<HomeData>((ref) async {
  final repository = ref.watch(homeRepositoryProvider);
  final supabase = ref.watch(supabaseClientProvider);
  
  final userId = supabase.auth.currentUser?.id;
  if (userId == null) {
    throw Exception('User not authenticated');
  }
  
  return repository.getHomeData(userId);
});

final selectedWorkoutCategoryProvider = StateProvider<String>((ref) => 'All');

final filteredRecommendedWorkoutsProvider = Provider<List<WorkoutSummary>>((ref) {
  final homeDataAsync = ref.watch(homeDataProvider);
  final selectedCategory = ref.watch(selectedWorkoutCategoryProvider);
  
  return homeDataAsync.when(
    data: (homeData) {
      if (selectedCategory == 'All') {
        return homeData.recommendedWorkouts;
      }
      return homeData.recommendedWorkouts
          .where((workout) => workout.category == selectedCategory)
          .toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});